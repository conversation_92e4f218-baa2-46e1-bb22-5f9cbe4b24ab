import { configureStore } from '@reduxjs/toolkit';
import authReducer from './authSlice';
import profileReducer from './profileSlice';
import policyReducer from './policySlice';
import documentReducer from './documentSlice';
import notificationReducer from './notificationSlice';
import applicationFlowReducer from './applicationFlowSlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    profile: profileReducer,
    policy: policyReducer,
    document: documentReducer,
    notification: notificationReducer,
    applicationFlow: applicationFlowReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
